<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .headerHeight{
        height: 100px;
    }
    .table-header{
        height: 100px;
        background-color: grey;
        font-size: larger;
    }
    .sales-header{
        border-right: 3px solid white;
        font-size: 20px;
        text-align: center;
        background: #26a69a;
        font-weight: 600;
        color: white;
    }
    .sales-row-side-border{
        border-right: 3px solid white;
    }
    .center-text-table{
        text-align: center;
        border: 1px solid #26a29a;
        background: #26a69a70;
    }
    .modal-large {
        width:90% !important;
    }
    .custom-modal {
        width:90% !important;
    }

    .ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
  <div class="col s12">
    <div class="row">
      <div class="col s12">
          <h4 class="left">Create Reference Order <a style="margin-top:5px" class="btn btn-small vBtn" href="https://drive.google.com/file/d/1ikggrSJTs7I6WarFDI2tjVAZTY0r_4tf/view?usp=sharing" target="_blank">Suggestive Ordering Guide</a></h4>
        <div class="right-align" style="margin-top: 20px;">
          <input type="button" class="btn" data-ng-show="showScmProductsList"
                 value="BACK" data-ng-click="goToFulfilmentSelection()"/>
        </div>

      </div>
    </div>
  </div>

  <div class="row">
    <div class="col s12 menuItemList" data-ng-show="!showScmProductsList">
      <div class="form-element">
        <div class="row">
          <div class="col s2"><label>Fulfillment date</label></div>
          <div class="col s4"><input input-date type="text" name="created" id="inputCreated"
                                     ng-model="fulfillmentDate"
                                     container="" format="yyyy-mm-dd" select-years="1"
                                     min="{{minRefOrderFulFillmentDate}}"
                                     max="{{maxRefOrderFulFillmentDate}}"
                                     data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
          <div class="col s6"><label class="highlight-data">{{fulfillmentDay}}</label></div>
        </div>
        <div class="form-element">
          <div class="row">
            <div class="col s2 ">
              <label>Select Brand</label>
            </div>
            <div class="col s4">
              <select data-ng-model="selectedBrandDetails" class='form-control'
                      data-ng-options="brand as brand.brandName for brand in brandDetails"
                      data-ng-change="setBrand(selectedBrandDetails)">
              </select>
            </div>
          </div>
        </div>
        <div class="form-element">
          <div class="row">
            <div class="col s2"><label>Ordering Days</label></div>
            <div class="col s4"><input type="number" min="1" placeholder="No Of Days"
                                       name="noOfDays"
                                       id="noOfDays" ng-model="noOfDays"
                                       ng-disabled="isApiInProgress"
                                       data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
          </div>
        </div>
        <div class="form-element">
          <div class="row">
            <div class="col s2"><label>Order Will Last Until</label></div>
            <div class="col s4"><label>{{stockLastingDate | date : 'yyyy-MM-dd'}}</label></div>
            <div class="col s6"><label class="highlight-data">{{stockLastingDay}}</label></div>
          </div>

        </div>

        <div class="form-element">
          <div class="row">
            <label class="highlight-row" style="margin-top: 20px">Doing ordering for the below days</label>
            <table class="table table-striped table-bordered">
              <thead>
                <tr class="collection-item list-head">
                  <th>Day Type</th>
                  <th>Date</th>
                  <th>Ordering Percentage</th>
                </tr>
              </thead>
              <tbody>
              <tr ng-repeat="entry in dataEntry"
                  data-ng-class="{'red':entry.dayType=='REMAINING_DAY','green':entry.dayType=='ORDERING_DAY'}">
                <td>{{entry.dayType}}</td>
                <td>{{entry.date}}</td>
                <td>
                  <select data-ng-model="entry.orderingPercentage" data-ng-disabled="entry.dayType=='REMAINING_DAY'"
                          data-ng-options="orderPercentage as orderPercentage for orderPercentage in orderingPercentages"
                          data-ng-change="setSelectedOrderingPercentage(entry, entry.orderingPercentage)"></select>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="row">
            <input type="button" class="btn" value="GUIDE ME" data-ng-disabled="isFulfillmentInputInvalidOrLoading()" data-ng-click="getSuggestedQuantities()"/>
        </div>
      </div>
    </div>
    <div class="col s12 menuItemList" data-ng-show="showScmProductsList">

      <div class="row" style="margin-bottom: 10px;">
        <div class="row">
          <div class="col s2"><label>Ordering Days</label> {{noOfDays}}</div>
          <div class="col s4"><label>Fulfillment Date</label> {{fulfillmentDate | date : 'dd-MM-yyyy'}}
            ({{fulfillmentDay}})
          </div>
          <div class="col s4"><label>Order Will Last Until</label>{{stockLastingDate | date :
            'dd-MM-yyyy'}}
            ({{stockLastingDay}})
          </div>
          <div class="col s2" data-ng-if="currentUser == 140199 || currentUser == 127707 || currentUser == 120063">
            <button data-ng-click="viewDetailedExpiries()" tooltipped data-tooltip="View Detailed Expiry"
                    class="btn btn-medium green">Expiries <i class="material-icons">data_thresholding</i></button>
          </div>
        </div>
        <h4>Kitchen Items</h4>
        <ul class="collection z-depth-1-half" style="font-size: 12px;">
          <li class="collection-item list-head">
            <div class="row">
              <div class="col s2">Product Name (UOM/ SUB CATEGORY)</div>
              <div class="col s1">
                In Stock
              </div>
              <div class="col s1">
                In Transit
              </div>
              <div class="col s1">
                Ack RO
              </div>
              <div class="col s1">Predicted Consp*</div>
              <div class="col s1">Suggested Consp* Ordering</div>
              <div class="col s1">Ordering</div>
              <div class="col s1">Final Ordering Qty</div>
              <div class="col s1">Packaging Name</div>
              <div class="col s1">Expiry</div>
              <div class="col s1">Expiry Usage</div>
            </div>
          </li>
          <li data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'KITCHEN'} | orderBy:['productName'] track by $index"
              data-ng-class="{'red lighten-3 collection-item': scmProduct.discontinuedProduct,'collection-item' : !scmProduct.discontinuedProduct}">
            <div class="row">
              <div class="col s2">
                <a data-ng-click="showPreview($event, scmProduct.productId,'PRODUCT')">{{scmProduct.productName}} ({{scmProduct.unitOfMeasure}} / {{scmProduct.subCategoryDefinition.name}})</a>
                <span data-ng-if="scmProduct.discontinuedProduct">*</span>
              </div>
              <div class="col s1" title="Expiry Product : {{productWiseStock[scmProduct.id].stockAtHand}}">
                {{scmProduct.stockAtHand.totalStock || '0'}}
              </div>
              <div class="col s1" title="Expiry Product : {{productWiseStock[scmProduct.id].inTransit}}">
                {{scmProduct.inTransit.totalStock || '0'}}
              </div>
              <div class="col s1">
                <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[scmProduct.id] || '0'}})" data-ng-click="showAcknowledgedRoStock(scmProduct)" data-target='viewAcknowledgedRoStockModal' modal/>
              </div>
              <div class="col s1">{{scmProduct.predictedQuantity | number : 2}}</div>
              <div class="col s1">{{scmProduct.suggestedOrderingQuantity | number : 2}}</div>
              <div class="col s1 orderingColumnHighlight">
                {{scmProduct.orderingQuantity | number : 2}}
              </div>
              <div class="col s1">
                <input type="text" data-ng-model="scmProduct.packagingQuantity"
                       data-ng-disabled="isOrderingNonDisable(scmProduct.productId)"
                       data-ng-change="updateOrderingQty(scmProduct)"
                       data-ng-attr-title="{{ isOrderingNonDisable(scmProduct.  productId) ? 'cannot change the quantity of this product' : undefined }}"/>
              </div>
              <div class="col s1">{{scmProduct.packagingName}}</div>
              <div class="col s1 ellipsis" title="Expiry Product : {{scmProduct.allExpiryData || '-'}}">{{scmProduct.allExpiryData || '-'}}</div>
              <div class="col s1">
                <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(scmProduct)" data-target='viewProductFulfilmentLogsModal' modal/>
              </div>
            </div>
          </li>
        </ul>
        <hr>
        <div class="col s12">
          <div class="col s4">
            <h4>Warehouse Items</h4>
          </div>
          <div class="col s8" style="margin-top: 20px;">
            <input id="whOrdering" type="checkbox" data-ng-model="warehouseordering" data-ng-change="hideWareHouseItems(warehouseordering)">
            <label for="whOrdering">Order Items From Warehouse</label>
          </div>
        </div>
        <ul class="collection z-depth-1-half" style="font-size: 12px;" data-ng-if="warehouseordering">
          <li class="collection-item list-head">
            <div class="row">
              <div class="col s2">Product Name (UOM/ SUB CATEGORY)</div>
              <div class="col s1">
                In Stock
              </div>
              <div class="col s1">
                In Transit
              </div>
              <div class="col s1">
                Ack RO
              </div>
              <div class="col s1">Predicted Consp*</div>
              <div class="col s1">Suggested Consp* Ordering</div>
              <div class="col s1">Ordering</div>
              <div class="col s1">Final Ordering Qty</div>
              <div class="col s1">Packaging Name</div>
              <div class="col s1">Expiry</div>
              <div class="col s1">Expiry Usage</div>
            </div>
          </li>
          <li class="collection-item"
              data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'WAREHOUSE'} | orderBy:['productName'] track by $index"
              data-ng-class="{'red lighten-3 collection-item': scmProduct.discontinuedProduct,'collection-item' : !scmProduct.discontinuedProduct}">
            <div class="row">
              <div class="col s2">
                <a data-ng-click="showPreview($event, scmProduct.productId,'PRODUCT')">{{scmProduct.productName}} ({{scmProduct.unitOfMeasure}} / {{scmProduct.subCategoryDefinition.name}})</a>
                <span data-ng-if="scmProduct.discontinuedProduct">*</span>
              </div>
              <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                {{scmProduct.stockAtHand.totalStock || '0'}}
              </div>
              <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                {{scmProduct.inTransit.totalStock || '0'}}
              </div>
              <div class="col s1">
                <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[scmProduct.id] || '0'}})" data-ng-click="showAcknowledgedRoStock(scmProduct)" data-target='viewAcknowledgedRoStockModal' modal/>
              </div>
              <div class="col s1">{{scmProduct.predictedQuantity | number : 2}}</div>
              <div class="col s1">{{scmProduct.suggestedOrderingQuantity | number : 2}}</div>
              <div class="col s1 orderingColumnHighlight">
                {{scmProduct.orderingQuantity | number : 2}}
              </div>
              <div class="col s1">
                <input type="text" data-ng-model="scmProduct.packagingQuantity" data-ng-change="updateOrderingQty(scmProduct)"/>
              </div>
              <div class="col s1">{{scmProduct.packagingName}}</div>
              <div class="col s1 ellipsis" title="Expiry Product : {{scmProduct.allExpiryData || '-'}}">{{scmProduct.allExpiryData || '-'}}</div>
              <div class="col s1">
              <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(scmProduct)" data-target='viewProductFulfilmentLogsModal' modal/>
              </div>
              </div>
          </li>
        </ul>
        <hr>

        <div class="col s12" data-ng-if="specializedRoProducts.length > 0">
          <div class="col s6">
            <h4>Specialized Ordering Suggestions</h4>
          </div>
          <div class="col s12" data-ng-if="specializedRoProducts.length > 0">
            <div class="col s12">
              <div style="padding: 10px; margin: 10px; border-radius: 4px; background: red; color: white">RO will not be Placed For Following Products. Please Find the Suggestions of Specialized RO Products....!</div>
            </div>
          </div>
          <ul class="collection z-depth-1-half" style="font-size: 12px;" data-ng-if="specializedRoProducts.length > 0">
            <li class="collection-item list-head">
              <div class="row">
                <div class="col s2">Product Name (UOM/ SUB CATEGORY)</div>
                <div class="col s1">
                  In Stock
                </div>
                <div class="col s1">
                  In Transit
                </div>
                <div class="col s1">
                  Ack RO
                </div>
                <div class="col s1">Predicted Consp*</div>
                <div class="col s1">Suggested Consp* Ordering</div>
                <div class="col s1">Ordering</div>
                <div class="col s1">Final Ordering Qty</div>
                <div class="col s1">Packaging Name</div>
                <div class="col s1">Expiry</div>
                <div class="col s1">Expiry Usage</div>
              </div>
            </li>
            <li class="collection-item"
                data-ng-repeat="scmProduct in specializedRoProducts | orderBy:['productName'] track by $index"
                data-ng-class="{'red lighten-3 collection-item': scmProduct.discontinuedProduct,'collection-item' : !scmProduct.discontinuedProduct}">
              <div class="row">
                <div class="col s2">
                  <a data-ng-click="showPreview($event, scmProduct.productId,'PRODUCT')">{{scmProduct.productName}} ({{scmProduct.unitOfMeasure}} / {{scmProduct.subCategoryDefinition.name}})</a>
                  <span data-ng-if="scmProduct.discontinuedProduct">*</span>
                </div>
                <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                  {{scmProduct.stockAtHand.totalStock || '0'}}
                </div>
                <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                  {{scmProduct.inTransit.totalStock || '0'}}
                </div>
                <div class="col s1">
                  <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[scmProduct.id] || '0'}})" data-ng-click="showAcknowledgedRoStock(scmProduct)" data-target='viewAcknowledgedRoStockModal' modal/>
                </div>
                <div class="col s1">{{scmProduct.predictedQuantity | number : 2}}</div>
                <div class="col s1">{{scmProduct.suggestedOrderingQuantity | number : 2}}</div>
                <div class="col s1 orderingColumnHighlight">
                  {{scmProduct.orderingQuantity | number : 2}}
                </div>
                <div class="col s1">
                  {{scmProduct.packagingQuantity | number : 2}}
                </div>
                <div class="col s1">{{scmProduct.packagingName}}</div>
                <div class="col s1 ellipsis" title="Expiry Product : {{scmProduct.allExpiryData || '-'}}">{{scmProduct.allExpiryData || '-'}}</div>
                <div class="col s1">
                  <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(scmProduct)" data-target='viewProductFulfilmentLogsModal' modal/>
                </div>
              </div>
            </li>
          </ul>
      </div>
      <div class="row">
        <div class="col s12">
          <div class="form-element">
            <label>Comment(optional)</label>
            <textarea data-ng-model="comment"></textarea>
          </div>
          <div class="form-element">
            <input type="button" class="btn" value="BACK" data-ng-click="goToFulfilmentSelection()"/>
            <!--<input type="button" class="btn" value="SAVE" data-ng-click="sendReferenceOrder('INITIATED')" />-->
            <input type="button" class="btn" value="SUBMIT"
                   data-ng-click="sendReferenceOrder('CREATED')"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/ng-template" id="expiriesModal.html" class="modal-large">
  <div class="modal-content" style="overflow-x: auto;">
    <div class="row">
      <input type="text" data-ng-model="searchProducts" placeholder="Enter Product Name to search">
    </div>
    <div class="row">
      <table class="bordered striped">
        <thead>
        <tr>
          <th data-ng-repeat="column in colHeaders">
            {{column}}
          </th>
        </tr>
        </thead>
        <tbody>
        <tr data-ng-repeat="row in displayRows | filter:searchProducts">
          <td>{{row.productName}}</td>
          <td data-ng-repeat="currentDate in dates">
            {{row[currentDate.date + "_suggested"]}},
            {{row[currentDate.date + "_inStock"]}},
            {{row[currentDate.date + "_inTransit"]}}
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-medium red right" data-ng-click="close()">Close</button>
  </div>
</script>

<div id="viewProductFulfilmentLogsModal" class="modal">
  <div class="modal-content">
    <h4>Expiry Usage Of {{selectedProductForLogs.productName}}</h4>
    <div class="row">
      <div class="col s12">
        <ul>
          <li data-ng-repeat="log in selectedProductLogs track by $index"
              style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
            {{log}}
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-small modal-action modal-close">Close</button>
  </div>
</div>

<div id="viewAcknowledgedRoStockModal" class="modal">
  <div class="modal-content">
    <h4>Acknowledge RO Quantity Of {{selectedProductForAcknowledgedRo.productName}}</h4>
      <div class="row" data-ng-repeat="fulfilmentDate in OrderingDaysFinal track by $index">
      <div class="col s12" style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
        Fulfilment Date : {{fulfilmentDate}}
      </div>
      <div class="col s12">
        <table class="table table-striped table-bordered">
          <thead style="background-color: #e7e7e7">
          <th data-ng-repeat="innerFulfilmentDate in getArray(OrderingDaysFinal,$index)">{{innerFulfilmentDate}}</th>
          </thead>
          <tbody>
          <tr>
            <td data-ng-repeat="innerFulfilmentDate in getArray(OrderingDaysFinal,$index)">
              <input type="text" style="color: red" data-ng-disabled="true" value="{{acknowledgedStockMap[selectedProductForAcknowledgedRo.id  + '_' + fulfilmentDate][innerFulfilmentDate] || '-'}}"/>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <hr>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-small modal-action modal-close">Close</button>
  </div>
</div>
