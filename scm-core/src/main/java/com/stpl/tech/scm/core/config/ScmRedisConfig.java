package com.stpl.tech.scm.core.config;

import com.stpl.tech.scm.core.redis.impl.RedisPublisherImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.convert.*;
import org.springframework.data.redis.core.mapping.RedisMappingContext;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Arrays;


@Configuration
@ComponentScan(basePackages = {"com.stpl.tech.scm.core.redis"})
public class ScmRedisConfig {

        @Autowired
        private Environment env;

        public ScmRedisConfig() {
            super();
        }


        @Bean
        public JedisConnectionFactory jedisConnectionFactory() {
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(env.getProperty("redis.host"), Integer.valueOf(env.getProperty("redis.port")));
            config.setDatabase(Integer.parseInt(env.getProperty("redis.db.index")));
            JedisConnectionFactory factory =  new JedisConnectionFactory(config);
            factory.getPoolConfig().setMaxIdle(30);
            factory.getPoolConfig().setMinIdle(10);
            factory.setUsePool(true);
            return  factory;
        }

    @Bean
    public CacheManager cacheManager() {
        return RedisCacheManager.RedisCacheManagerBuilder.fromConnectionFactory(jedisConnectionFactory())
                .build();
    }

        @Bean
        RedisTemplate<String, Object> redisTemplate() {
            final RedisTemplate<String, Object> template = new RedisTemplate<String, Object>();
            template.setConnectionFactory(jedisConnectionFactory());
            template.setKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(new GenericToStringSerializer<>(Object.class));
            template.setValueSerializer(new GenericToStringSerializer<Object>(Object.class));
            return template;
        }

        @Bean
        RedisMessageListenerContainer redisContainer() {
            final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
            container.setConnectionFactory(jedisConnectionFactory());
            return container;
        }

        @Bean
        RedisPublisherImpl redisPublisher() {
            return new RedisPublisherImpl(redisTemplate());
        }

    @Bean
    public MappingRedisConverter redisConverter(RedisMappingContext mappingContext,
                                                RedisCustomConversions customConversions, ReferenceResolver referenceResolver) {

        MappingRedisConverter mappingRedisConverter = new MappingRedisConverter(mappingContext, null, referenceResolver,
                customTypeMapper());
        mappingRedisConverter.setCustomConversions(redisCustomConversions());
        return mappingRedisConverter;
    }

    @Bean
    public RedisTypeMapper customTypeMapper() {
        return new CustomRedisTypeMapper();
    }

    public RedisCustomConversions redisCustomConversions() {
        return new RedisCustomConversions(
                Arrays.asList(new DateToBytesConverter(),new BytesToDateConverter()));
    }

    class CustomRedisTypeMapper extends DefaultRedisTypeMapper {

    }


}
